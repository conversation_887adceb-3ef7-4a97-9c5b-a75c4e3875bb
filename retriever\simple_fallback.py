
class SimpleFallbackRetriever:
    def __init__(self):
        self.knowledge_base = {
            "变压器": "变压器是电力系统重要设备，主要故障包括绝缘击穿、过热、局部放电等。",
            "断路器": "断路器是电力系统保护设备，常见故障有拒动、误动、SF6泄漏等。",
            "保护": "电力系统保护遵循四性原则：选择性、速动性、可靠性、灵敏性。"
        }
    
    def search(self, query, top_k=5):
        results = []
        for i, (key, content) in enumerate(self.knowledge_base.items()):
            if key in query:
                results.append({
                    "id": f"fallback_{i}",
                    "content": content,
                    "score": 0.8,
                    "title": f"{key}相关知识"
                })
        
        if not results:
            results.append({
                "id": "fallback_general",
                "content": f"关于'{query}'的电力系统技术分析和处理建议。",
                "score": 0.6,
                "title": "通用技术指导"
            })
        
        class MockResponse:
            def __init__(self, results):
                self.success = True
                self.results = [type('obj', (object,), r) for r in results]
        
        return MockResponse(results[:top_k])

simple_fallback_retriever = SimpleFallbackRetriever()
