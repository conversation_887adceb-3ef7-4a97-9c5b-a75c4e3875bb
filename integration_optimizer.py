#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成优化器 - 专门解决端到端集成问题
确保各组件能够协调工作，提升整体系统性能
"""

import os
import sys
import json
import logging
import time
from pathlib import Path
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class IntegrationOptimizer:
    """集成优化器"""
    
    def __init__(self):
        self.components = {}
        self.integration_status = {}
        
    def initialize_components(self) -> Dict[str, Any]:
        """初始化所有组件"""
        logger.info("🔧 初始化系统组件...")
        
        # 1. 初始化统一检索器
        try:
            from retriever.unified_professional_retriever import get_unified_retriever
            self.components["retriever"] = get_unified_retriever()
            self.integration_status["retriever"] = {"status": "success", "available": True}
            logger.info("✅ 统一检索器初始化成功")
        except Exception as e:
            self.integration_status["retriever"] = {"status": "failed", "error": str(e)}
            logger.error(f"❌ 统一检索器初始化失败: {e}")
        
        # 2. 初始化专业提示词模板
        try:
            from langchain_modules.prompts.professional_prompt_templates import professional_prompt_templates
            self.components["prompt_templates"] = professional_prompt_templates
            self.integration_status["prompt_templates"] = {"status": "success", "available": True}
            logger.info("✅ 专业提示词模板初始化成功")
        except Exception as e:
            self.integration_status["prompt_templates"] = {"status": "failed", "error": str(e)}
            logger.error(f"❌ 专业提示词模板初始化失败: {e}")
        
        # 3. 初始化DeepSeek-R1推理优化器
        try:
            from langchain_modules.reasoning.deepseek_r1_optimizer import deepseek_r1_optimizer
            self.components["reasoning_optimizer"] = deepseek_r1_optimizer
            self.integration_status["reasoning_optimizer"] = {"status": "success", "available": True}
            logger.info("✅ DeepSeek-R1推理优化器初始化成功")
        except Exception as e:
            self.integration_status["reasoning_optimizer"] = {"status": "failed", "error": str(e)}
            logger.error(f"❌ DeepSeek-R1推理优化器初始化失败: {e}")
        
        # 4. 初始化数据处理器
        try:
            from data_processing.intelligent_data_processor import intelligent_data_processor
            self.components["data_processor"] = intelligent_data_processor
            self.integration_status["data_processor"] = {"status": "success", "available": True}
            logger.info("✅ 智能数据处理器初始化成功")
        except Exception as e:
            self.integration_status["data_processor"] = {"status": "failed", "error": str(e)}
            logger.error(f"❌ 智能数据处理器初始化失败: {e}")
        
        # 5. 初始化输出格式化器
        try:
            from output_formatting.professional_report_formatter import professional_report_formatter
            self.components["report_formatter"] = professional_report_formatter
            self.integration_status["report_formatter"] = {"status": "success", "available": True}
            logger.info("✅ 专业报告格式化器初始化成功")
        except Exception as e:
            self.integration_status["report_formatter"] = {"status": "failed", "error": str(e)}
            logger.error(f"❌ 专业报告格式化器初始化失败: {e}")
        
        return self.integration_status
    
    def test_component_integration(self) -> Dict[str, Any]:
        """测试组件集成"""
        logger.info("🧪 测试组件集成...")
        
        integration_results = {}
        
        # 测试用例
        test_case = {
            "query": "110kV变压器A相套管发生闪络故障",
            "context": {
                "equipment_info": "110kV/10kV变压器，容量31.5MVA",
                "fault_description": "A相套管发生闪络，保护动作跳闸",
                "monitoring_data": "跳闸前A相电流突增至2.3kA"
            }
        }
        
        # 1. 测试检索 -> 提示词生成流程
        integration_results["retrieval_to_prompt"] = self._test_retrieval_to_prompt(test_case)
        
        # 2. 测试提示词 -> 推理优化流程
        integration_results["prompt_to_reasoning"] = self._test_prompt_to_reasoning(test_case)
        
        # 3. 测试数据处理 -> 报告生成流程
        integration_results["processing_to_report"] = self._test_processing_to_report(test_case)
        
        # 4. 测试完整流程
        integration_results["full_pipeline"] = self._test_full_pipeline(test_case)
        
        return integration_results
    
    def _test_retrieval_to_prompt(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """测试检索到提示词生成的流程"""
        try:
            # 1. 执行检索
            if "retriever" not in self.components:
                return {"success": False, "error": "检索器不可用"}
            
            retriever = self.components["retriever"]
            retrieval_response = retriever.search(test_case["query"], top_k=3)
            
            if not retrieval_response.success:
                return {"success": False, "error": "检索失败"}
            
            # 2. 使用检索结果生成提示词
            if "prompt_templates" not in self.components:
                return {"success": False, "error": "提示词模板不可用"}
            
            prompt_templates = self.components["prompt_templates"]
            template = prompt_templates.get_template("deepseek_v3_fault_analysis")
            
            # 构建上下文
            context = {
                "question": test_case["query"],
                "fault_description": test_case["context"]["fault_description"],
                "equipment_info": test_case["context"]["equipment_info"],
                "monitoring_data": test_case["context"]["monitoring_data"],
                "historical_data": "检索到的相关信息：" + "\n".join([r.content[:100] for r in retrieval_response.results[:2]]),
                "image_analysis": ""
            }
            
            prompt = template.format(**context)
            
            return {
                "success": True,
                "retrieval_results_count": len(retrieval_response.results),
                "prompt_length": len(prompt),
                "integration_quality": min(len(retrieval_response.results) / 3 + len(prompt) / 1000, 1.0)
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _test_prompt_to_reasoning(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """测试提示词到推理优化的流程"""
        try:
            # 1. 生成提示词
            if "prompt_templates" not in self.components:
                return {"success": False, "error": "提示词模板不可用"}
            
            prompt_templates = self.components["prompt_templates"]
            template = prompt_templates.get_template("deepseek_r1_fault_analysis")
            
            context = {
                "question": test_case["query"],
                "fault_description": test_case["context"]["fault_description"],
                "equipment_info": test_case["context"]["equipment_info"],
                "monitoring_data": test_case["context"]["monitoring_data"],
                "historical_data": "历史数据分析",
                "image_analysis": ""
            }
            
            prompt = template.format(**context)
            
            # 2. 模拟推理内容
            reasoning_content = f"""
            <think>
            分析{test_case['query']}：
            1. 故障现象：{test_case['context']['fault_description']}
            2. 设备信息：{test_case['context']['equipment_info']}
            3. 可能原因：绝缘老化、过电压冲击
            4. 处理建议：立即停运、更换套管、加强监测
            </think>
            
            <answer>
            基于分析，该故障为套管绝缘击穿，建议立即处理。
            </answer>
            """
            
            # 3. 推理优化
            if "reasoning_optimizer" not in self.components:
                return {"success": False, "error": "推理优化器不可用"}
            
            reasoning_optimizer = self.components["reasoning_optimizer"]
            optimization_result = reasoning_optimizer.optimize_reasoning_chain(reasoning_content, context)
            
            return {
                "success": True,
                "prompt_length": len(prompt),
                "reasoning_quality": optimization_result.get("quality_score", 0.0),
                "improvements_applied": optimization_result.get("improvements_applied", False),
                "integration_quality": (len(prompt) / 1000 + optimization_result.get("quality_score", 0.0)) / 2
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _test_processing_to_report(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """测试数据处理到报告生成的流程"""
        try:
            # 1. 数据处理
            if "data_processor" not in self.components:
                return {"success": False, "error": "数据处理器不可用"}
            
            data_processor = self.components["data_processor"]
            document_content = f"{test_case['query']} {test_case['context']['fault_description']} {test_case['context']['equipment_info']}"
            annotation = data_processor.process_document(document_content)
            
            # 2. 报告生成
            if "report_formatter" not in self.components:
                return {"success": False, "error": "报告格式化器不可用"}
            
            report_formatter = self.components["report_formatter"]
            
            # 构建报告数据
            report_data = {
                "equipment_name": "110kV变压器",
                "fault_type": "套管闪络",
                "fault_description": test_case["context"]["fault_description"],
                "equipment_info": test_case["context"]["equipment_info"],
                "technical_terms": annotation.technical_terms,
                "quality_score": annotation.quality_metrics.overall_score
            }
            
            report = report_formatter.format_fault_analysis_report(report_data)
            
            return {
                "success": True,
                "data_quality": annotation.quality_metrics.overall_score,
                "technical_terms_count": len(annotation.technical_terms),
                "report_length": len(report),
                "integration_quality": (annotation.quality_metrics.overall_score + min(len(report) / 1000, 1.0)) / 2
            }
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _test_full_pipeline(self, test_case: Dict[str, Any]) -> Dict[str, Any]:
        """测试完整流程"""
        try:
            pipeline_results = {}
            
            # 1. 检索阶段
            if "retriever" in self.components:
                retriever = self.components["retriever"]
                retrieval_response = retriever.search(test_case["query"], top_k=3)
                pipeline_results["retrieval"] = {
                    "success": retrieval_response.success,
                    "results_count": len(retrieval_response.results) if retrieval_response.success else 0
                }
            else:
                pipeline_results["retrieval"] = {"success": False, "results_count": 0}
            
            # 2. 提示词生成阶段
            if "prompt_templates" in self.components:
                prompt_templates = self.components["prompt_templates"]
                template = prompt_templates.get_template("deepseek_v3_fault_analysis")
                
                context = {
                    "question": test_case["query"],
                    "fault_description": test_case["context"]["fault_description"],
                    "equipment_info": test_case["context"]["equipment_info"],
                    "monitoring_data": test_case["context"]["monitoring_data"],
                    "historical_data": "相关历史数据",
                    "image_analysis": ""
                }
                
                prompt = template.format(**context)
                pipeline_results["prompt_generation"] = {
                    "success": True,
                    "prompt_length": len(prompt)
                }
            else:
                pipeline_results["prompt_generation"] = {"success": False, "prompt_length": 0}
            
            # 3. 数据处理阶段
            if "data_processor" in self.components:
                data_processor = self.components["data_processor"]
                document_content = f"{test_case['query']} {test_case['context']['fault_description']}"
                annotation = data_processor.process_document(document_content)
                pipeline_results["data_processing"] = {
                    "success": True,
                    "quality_score": annotation.quality_metrics.overall_score
                }
            else:
                pipeline_results["data_processing"] = {"success": False, "quality_score": 0.0}
            
            # 4. 报告生成阶段
            if "report_formatter" in self.components:
                report_formatter = self.components["report_formatter"]
                report_data = {
                    "equipment_name": "110kV变压器",
                    "fault_type": "套管故障",
                    "fault_description": test_case["context"]["fault_description"]
                }
                report = report_formatter.format_fault_analysis_report(report_data)
                pipeline_results["report_generation"] = {
                    "success": True,
                    "report_length": len(report)
                }
            else:
                pipeline_results["report_generation"] = {"success": False, "report_length": 0}
            
            # 计算整体集成质量
            successful_stages = sum(1 for stage in pipeline_results.values() if stage["success"])
            total_stages = len(pipeline_results)
            integration_quality = successful_stages / total_stages if total_stages > 0 else 0.0
            
            return {
                "success": integration_quality >= 0.5,
                "pipeline_results": pipeline_results,
                "successful_stages": successful_stages,
                "total_stages": total_stages,
                "integration_quality": integration_quality
            }
            
        except Exception as e:
            return {"success": False, "error": str(e), "integration_quality": 0.0}
    
    def optimize_integration(self) -> Dict[str, Any]:
        """优化集成"""
        logger.info("🔧 优化系统集成...")
        
        optimization_results = {}
        
        # 1. 检查组件可用性
        available_components = [name for name, status in self.integration_status.items() 
                              if status.get("status") == "success"]
        
        optimization_results["available_components"] = available_components
        optimization_results["availability_rate"] = len(available_components) / len(self.integration_status)
        
        # 2. 测试组件集成
        integration_test_results = self.test_component_integration()
        optimization_results["integration_tests"] = integration_test_results
        
        # 3. 计算整体集成分数
        integration_scores = []
        for test_name, test_result in integration_test_results.items():
            if test_result.get("success", False):
                score = test_result.get("integration_quality", 0.5)
                integration_scores.append(score)
            else:
                integration_scores.append(0.0)
        
        overall_integration_score = sum(integration_scores) / len(integration_scores) if integration_scores else 0.0
        optimization_results["overall_integration_score"] = overall_integration_score
        
        # 4. 生成优化建议
        optimization_results["recommendations"] = self._generate_optimization_recommendations(
            available_components, integration_test_results, overall_integration_score
        )
        
        return optimization_results
    
    def _generate_optimization_recommendations(self, available_components: List[str], 
                                             integration_tests: Dict[str, Any], 
                                             overall_score: float) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 基于可用组件数量
        if len(available_components) < len(self.integration_status):
            missing_components = set(self.integration_status.keys()) - set(available_components)
            recommendations.append(f"修复缺失组件: {', '.join(missing_components)}")
        
        # 基于集成测试结果
        for test_name, test_result in integration_tests.items():
            if not test_result.get("success", False):
                recommendations.append(f"修复{test_name}集成问题")
        
        # 基于整体分数
        if overall_score < 0.6:
            recommendations.append("全面检查组件间接口和数据流")
        elif overall_score < 0.8:
            recommendations.append("优化组件间数据传递效率")
        else:
            recommendations.append("系统集成良好，建议进行性能优化")
        
        return recommendations


def main():
    """主函数"""
    print("🚀 启动集成优化器")
    
    optimizer = IntegrationOptimizer()
    
    # 1. 初始化组件
    init_status = optimizer.initialize_components()
    print(f"\n📊 组件初始化状态:")
    for component, status in init_status.items():
        status_icon = "✅" if status["status"] == "success" else "❌"
        print(f"  {status_icon} {component}: {status['status']}")
    
    # 2. 优化集成
    optimization_results = optimizer.optimize_integration()
    
    print(f"\n📈 集成优化结果:")
    print(f"  可用组件率: {optimization_results['availability_rate']:.2f}")
    print(f"  整体集成分数: {optimization_results['overall_integration_score']:.2f}")
    
    print(f"\n💡 优化建议:")
    for i, recommendation in enumerate(optimization_results['recommendations'], 1):
        print(f"  {i}. {recommendation}")
    
    print("\n🎉 集成优化完成！")
    
    return optimization_results


if __name__ == "__main__":
    main()
