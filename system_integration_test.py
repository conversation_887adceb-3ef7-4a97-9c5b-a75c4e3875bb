#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统集成测试脚本 - 白银市电力故障诊断系统
验证所有优化组件的功能和性能，确保系统达到专业级水准

测试范围：
1. 统一检索接口功能测试
2. 专业提示词模板测试
3. DeepSeek-R1推理链优化测试
4. 高级RAG技术集成测试
5. 数据处理质量测试
6. 输出格式专业化测试
7. 系统配置统一化测试
"""

import os
import sys
import json
import time
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(str(Path(__file__).parent))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('logs/integration_test.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class SystemIntegrationTester:
    """系统集成测试器"""
    
    def __init__(self):
        self.test_results = {}
        self.overall_score = 0.0
        self.test_start_time = time.time()
        
        # 测试用例数据
        self.test_cases = {
            "fault_analysis": {
                "query": "110kV变压器A相套管发生闪络故障，请分析原因并提供处理建议",
                "context": {
                    "equipment_info": "110kV/10kV变压器，容量31.5MVA，投运15年",
                    "monitoring_data": "跳闸前A相电流突增至2.3kA，温度正常",
                    "fault_description": "A相套管发生闪络，保护动作跳闸",
                    "historical_data": "近期雷雨天气较多，该变压器曾有轻微放电现象"
                }
            },
            "equipment_inspection": {
                "query": "请分析断路器SF6气体压力异常的原因",
                "context": {
                    "equipment_info": "126kV SF6断路器，型号LW36-126",
                    "monitoring_data": "SF6气体压力0.45MPa，正常值0.6MPa",
                    "fault_description": "SF6气体压力持续下降",
                    "historical_data": "设备运行8年，上次检修2年前"
                }
            }
        }
    
    def run_all_tests(self) -> Dict[str, Any]:
        """运行所有测试"""
        logger.info("🚀 开始系统集成测试")
        
        # 1. 统一检索接口测试
        self.test_unified_retrieval()
        
        # 2. 专业提示词模板测试
        self.test_professional_prompts()
        
        # 3. DeepSeek-R1推理链测试
        self.test_deepseek_r1_reasoning()
        
        # 4. 高级RAG技术测试
        self.test_advanced_rag()
        
        # 5. 数据处理质量测试
        self.test_data_processing()
        
        # 6. 输出格式测试
        self.test_output_formatting()
        
        # 7. 系统配置测试
        self.test_system_configuration()
        
        # 8. 端到端集成测试
        self.test_end_to_end_integration()
        
        # 生成测试报告
        return self.generate_test_report()
    
    def test_unified_retrieval(self):
        """测试统一检索接口"""
        logger.info("📋 测试统一检索接口...")
        
        try:
            from retriever.unified_professional_retriever import get_unified_retriever, RetrievalStrategy
            
            # 初始化检索器
            retriever = get_unified_retriever()
            
            # 测试不同检索策略
            strategies = [RetrievalStrategy.HYBRID, RetrievalStrategy.PROFESSIONAL, RetrievalStrategy.SEMANTIC]
            strategy_results = {}
            
            for strategy in strategies:
                try:
                    start_time = time.time()
                    response = retriever.search(
                        query=self.test_cases["fault_analysis"]["query"],
                        strategy=strategy,
                        top_k=5
                    )
                    response_time = time.time() - start_time
                    
                    strategy_results[strategy.value] = {
                        "success": response.success,
                        "results_count": len(response.results),
                        "response_time": response_time,
                        "quality_score": response.query_analysis.complexity_score if response.success else 0.0
                    }
                    
                except Exception as e:
                    strategy_results[strategy.value] = {
                        "success": False,
                        "error": str(e),
                        "response_time": 0.0,
                        "quality_score": 0.0
                    }
            
            # 评估结果
            success_rate = sum(1 for result in strategy_results.values() if result["success"]) / len(strategies)
            avg_response_time = sum(result["response_time"] for result in strategy_results.values()) / len(strategies)
            avg_quality = sum(result["quality_score"] for result in strategy_results.values()) / len(strategies)
            
            self.test_results["unified_retrieval"] = {
                "success_rate": success_rate,
                "avg_response_time": avg_response_time,
                "avg_quality_score": avg_quality,
                "strategy_results": strategy_results,
                "score": (success_rate + min(1.0, 1.0 / max(avg_response_time, 0.1)) + avg_quality) / 3
            }
            
            logger.info(f"   ✅ 统一检索测试完成，成功率: {success_rate:.2f}")
            
        except Exception as e:
            logger.error(f"   ❌ 统一检索测试失败: {e}")
            self.test_results["unified_retrieval"] = {"score": 0.0, "error": str(e)}
    
    def test_professional_prompts(self):
        """测试专业提示词模板"""
        logger.info("📋 测试专业提示词模板...")
        
        try:
            from langchain_modules.prompts.professional_prompt_templates import professional_prompt_templates
            
            # 测试不同模板
            templates = ["deepseek_r1_fault_analysis", "deepseek_v3_fault_analysis"]
            template_results = {}
            
            for template_name in templates:
                try:
                    template = professional_prompt_templates.get_template(template_name)
                    
                    # 测试模板填充
                    context = self.test_cases["fault_analysis"]["context"]
                    context["question"] = self.test_cases["fault_analysis"]["query"]
                    
                    prompt = template.format(**context)
                    
                    # 评估提示词质量
                    quality_metrics = {
                        "length": min(len(prompt) / 2000, 1.0),
                        "structure": 1.0 if "<think>" in prompt or "##" in prompt else 0.5,
                        "professional": sum(1 for term in ["故障", "分析", "技术", "设备"] if term in prompt) / 4
                    }
                    
                    overall_quality = sum(quality_metrics.values()) / len(quality_metrics)
                    
                    template_results[template_name] = {
                        "success": True,
                        "prompt_length": len(prompt),
                        "quality_metrics": quality_metrics,
                        "overall_quality": overall_quality
                    }
                    
                except Exception as e:
                    template_results[template_name] = {
                        "success": False,
                        "error": str(e),
                        "overall_quality": 0.0
                    }
            
            # 评估结果
            success_rate = sum(1 for result in template_results.values() if result["success"]) / len(templates)
            avg_quality = sum(result["overall_quality"] for result in template_results.values()) / len(templates)
            
            self.test_results["professional_prompts"] = {
                "success_rate": success_rate,
                "avg_quality": avg_quality,
                "template_results": template_results,
                "score": (success_rate + avg_quality) / 2
            }
            
            logger.info(f"   ✅ 专业提示词测试完成，平均质量: {avg_quality:.2f}")
            
        except Exception as e:
            logger.error(f"   ❌ 专业提示词测试失败: {e}")
            self.test_results["professional_prompts"] = {"score": 0.0, "error": str(e)}
    
    def test_deepseek_r1_reasoning(self):
        """测试DeepSeek-R1推理链优化"""
        logger.info("📋 测试DeepSeek-R1推理链优化...")
        
        try:
            from langchain_modules.reasoning.deepseek_r1_optimizer import deepseek_r1_optimizer
            
            # 模拟推理内容
            test_reasoning = """
            第一步：观察故障现象，A相套管发生闪络，保护动作跳闸。
            第二步：分析可能原因，套管绝缘老化或外部过电压。
            第三步：结合监测数据，电流突增说明存在短路故障。
            第四步：考虑环境因素，近期雷雨天气可能导致过电压。
            第五步：综合分析，判断为套管绝缘击穿故障。
            """
            
            # 测试推理优化
            start_time = time.time()
            optimization_result = deepseek_r1_optimizer.optimize_reasoning_chain(
                test_reasoning, 
                self.test_cases["fault_analysis"]["context"]
            )
            optimization_time = time.time() - start_time
            
            # 评估优化效果
            quality_score = optimization_result.get("quality_score", 0.0)
            improvements_applied = optimization_result.get("improvements_applied", False)
            
            self.test_results["deepseek_r1_reasoning"] = {
                "optimization_time": optimization_time,
                "quality_score": quality_score,
                "improvements_applied": improvements_applied,
                "grpo_results": optimization_result.get("grpo_results", {}),
                "verification_results": optimization_result.get("verification_results", {}),
                "score": quality_score
            }
            
            logger.info(f"   ✅ DeepSeek-R1推理测试完成，质量分数: {quality_score:.2f}")
            
        except Exception as e:
            logger.error(f"   ❌ DeepSeek-R1推理测试失败: {e}")
            self.test_results["deepseek_r1_reasoning"] = {"score": 0.0, "error": str(e)}
    
    def test_advanced_rag(self):
        """测试高级RAG技术"""
        logger.info("📋 测试高级RAG技术...")
        
        try:
            from retriever.advanced_rag_engine import advanced_rag_engine, RAGTechnique
            
            # 测试不同RAG技术
            techniques = [RAGTechnique.HYDE, RAGTechnique.SELF_RAG, RAGTechnique.RAPTOR]
            
            start_time = time.time()
            response = advanced_rag_engine.enhanced_retrieval(
                query=self.test_cases["fault_analysis"]["query"],
                context=self.test_cases["fault_analysis"]["context"],
                techniques=techniques
            )
            response_time = time.time() - start_time
            
            self.test_results["advanced_rag"] = {
                "response_time": response_time,
                "results_count": len(response.results),
                "quality_score": response.quality_score,
                "techniques_used": [t.value for t in response.techniques_used],
                "reasoning_chain": response.reasoning_chain,
                "score": response.quality_score
            }
            
            logger.info(f"   ✅ 高级RAG测试完成，质量分数: {response.quality_score:.2f}")
            
        except Exception as e:
            logger.error(f"   ❌ 高级RAG测试失败: {e}")
            self.test_results["advanced_rag"] = {"score": 0.0, "error": str(e)}
    
    def test_data_processing(self):
        """测试数据处理质量"""
        logger.info("📋 测试数据处理质量...")
        
        try:
            from data_processing.intelligent_data_processor import intelligent_data_processor
            
            # 测试文档处理
            test_document = """
            110kV变压器A相套管故障分析报告
            
            设备信息：110kV/10kV变压器，容量31.5MVA
            故障现象：A相套管发生闪络，保护动作跳闸
            监测数据：跳闸前A相电流突增至2.3kA
            分析结论：套管绝缘击穿导致故障
            处理建议：更换故障套管，加强绝缘监测
            """
            
            annotation = intelligent_data_processor.process_document(
                test_document, 
                {"id": "test_doc_001", "type": "fault_report"}
            )
            
            # 评估处理质量
            quality_metrics = annotation.quality_metrics
            
            self.test_results["data_processing"] = {
                "document_type": annotation.document_type.value,
                "entities_count": len(annotation.entities),
                "relationships_count": len(annotation.relationships),
                "technical_terms_count": len(annotation.technical_terms),
                "quality_metrics": {
                    "overall_score": quality_metrics.overall_score,
                    "completeness": quality_metrics.completeness,
                    "accuracy": quality_metrics.accuracy,
                    "professional_depth": quality_metrics.professional_depth
                },
                "score": quality_metrics.overall_score
            }
            
            logger.info(f"   ✅ 数据处理测试完成，质量分数: {quality_metrics.overall_score:.2f}")
            
        except Exception as e:
            logger.error(f"   ❌ 数据处理测试失败: {e}")
            self.test_results["data_processing"] = {"score": 0.0, "error": str(e)}
    
    def test_output_formatting(self):
        """测试输出格式化"""
        logger.info("📋 测试输出格式化...")
        
        try:
            from output_formatting.professional_report_formatter import professional_report_formatter, OutputFormat
            
            # 测试故障分析报告格式化
            test_data = {
                "equipment_name": "110kV变压器",
                "fault_type": "套管闪络",
                "fault_description": "A相套管发生闪络故障",
                "equipment_parameters": {
                    "电压": {"value": 110, "unit": "kV", "status": "正常"},
                    "电流": {"value": 2.3, "unit": "kA", "status": "异常"}
                },
                "recommendations": ["更换故障套管", "加强监测"],
                "risk_assessment": {
                    "安全风险": {"level": "高", "description": "存在人员安全风险"}
                }
            }
            
            # 测试不同输出格式
            formats = [OutputFormat.STRUCTURED_TEXT, OutputFormat.HTML, OutputFormat.JSON]
            format_results = {}
            
            for output_format in formats:
                try:
                    report = professional_report_formatter.format_fault_analysis_report(
                        test_data, output_format
                    )
                    
                    format_results[output_format.value] = {
                        "success": True,
                        "report_length": len(report),
                        "contains_structure": "##" in report or "<h" in report or "{" in report
                    }
                    
                except Exception as e:
                    format_results[output_format.value] = {
                        "success": False,
                        "error": str(e)
                    }
            
            # 评估结果
            success_rate = sum(1 for result in format_results.values() if result["success"]) / len(formats)
            
            self.test_results["output_formatting"] = {
                "success_rate": success_rate,
                "format_results": format_results,
                "score": success_rate
            }
            
            logger.info(f"   ✅ 输出格式测试完成，成功率: {success_rate:.2f}")
            
        except Exception as e:
            logger.error(f"   ❌ 输出格式测试失败: {e}")
            self.test_results["output_formatting"] = {"score": 0.0, "error": str(e)}
    
    def test_system_configuration(self):
        """测试系统配置"""
        logger.info("📋 测试系统配置...")
        
        try:
            from configs.unified_system_config import unified_config, get_config, ComponentType
            
            # 测试配置访问
            config_tests = {
                "system_name": get_config("system.name"),
                "api_key": get_config("api.deepseek.api_key"),
                "retrieval_cache": get_config("retrieval.unified_retriever.cache_enabled"),
                "ui_port": get_config("ui.server.port")
            }
            
            # 测试组件配置
            component_configs = {
                "database": unified_config.get_component_config(ComponentType.DATABASE),
                "api": unified_config.get_component_config(ComponentType.API),
                "retrieval": unified_config.get_component_config(ComponentType.RETRIEVAL)
            }
            
            # 评估配置完整性
            config_completeness = sum(1 for value in config_tests.values() if value is not None) / len(config_tests)
            component_completeness = sum(1 for config in component_configs.values() if config) / len(component_configs)
            
            self.test_results["system_configuration"] = {
                "config_completeness": config_completeness,
                "component_completeness": component_completeness,
                "config_summary": unified_config.get_config_summary(),
                "score": (config_completeness + component_completeness) / 2
            }
            
            logger.info(f"   ✅ 系统配置测试完成，完整性: {config_completeness:.2f}")
            
        except Exception as e:
            logger.error(f"   ❌ 系统配置测试失败: {e}")
            self.test_results["system_configuration"] = {"score": 0.0, "error": str(e)}
    
    def test_end_to_end_integration(self):
        """端到端集成测试"""
        logger.info("📋 执行端到端集成测试...")
        
        try:
            # 模拟完整的故障分析流程
            test_query = self.test_cases["fault_analysis"]["query"]
            test_context = self.test_cases["fault_analysis"]["context"]
            
            # 1. 检索相关信息
            retrieval_success = False
            try:
                from retriever.unified_professional_retriever import get_unified_retriever
                retriever = get_unified_retriever()
                retrieval_response = retriever.search(test_query, top_k=3)
                retrieval_success = retrieval_response.success
            except:
                pass
            
            # 2. 生成专业提示词
            prompt_success = False
            try:
                from langchain_modules.prompts.professional_prompt_templates import professional_prompt_templates
                template = professional_prompt_templates.get_template("deepseek_v3_fault_analysis")
                prompt = template.format(question=test_query, **test_context)
                prompt_success = len(prompt) > 500
            except:
                pass
            
            # 3. 数据处理
            processing_success = False
            try:
                from data_processing.intelligent_data_processor import intelligent_data_processor
                annotation = intelligent_data_processor.process_document(test_query)
                processing_success = annotation.quality_metrics.overall_score > 0.5
            except:
                pass
            
            # 4. 输出格式化
            formatting_success = False
            try:
                from output_formatting.professional_report_formatter import professional_report_formatter
                report = professional_report_formatter.format_fault_analysis_report(test_context)
                formatting_success = len(report) > 1000
            except:
                pass
            
            # 计算集成成功率
            integration_components = [retrieval_success, prompt_success, processing_success, formatting_success]
            integration_score = sum(integration_components) / len(integration_components)
            
            self.test_results["end_to_end_integration"] = {
                "retrieval_success": retrieval_success,
                "prompt_success": prompt_success,
                "processing_success": processing_success,
                "formatting_success": formatting_success,
                "integration_score": integration_score,
                "score": integration_score
            }
            
            logger.info(f"   ✅ 端到端集成测试完成，集成分数: {integration_score:.2f}")
            
        except Exception as e:
            logger.error(f"   ❌ 端到端集成测试失败: {e}")
            self.test_results["end_to_end_integration"] = {"score": 0.0, "error": str(e)}
    
    def generate_test_report(self) -> Dict[str, Any]:
        """生成测试报告"""
        total_time = time.time() - self.test_start_time
        
        # 计算总体分数
        scores = [result.get("score", 0.0) for result in self.test_results.values()]
        self.overall_score = sum(scores) / len(scores) if scores else 0.0
        
        # 生成报告
        report = {
            "test_summary": {
                "total_tests": len(self.test_results),
                "overall_score": self.overall_score,
                "total_time": total_time,
                "test_date": datetime.now().isoformat()
            },
            "test_results": self.test_results,
            "recommendations": self._generate_recommendations(),
            "system_status": self._assess_system_status()
        }
        
        # 保存报告
        report_path = f"logs/integration_test_report_{int(time.time())}.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
        
        logger.info(f"🎉 系统集成测试完成！总体分数: {self.overall_score:.2f}")
        logger.info(f"📄 测试报告已保存: {report_path}")
        
        return report
    
    def _generate_recommendations(self) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        for test_name, result in self.test_results.items():
            score = result.get("score", 0.0)
            if score < 0.7:
                if test_name == "unified_retrieval":
                    recommendations.append("建议优化检索算法和索引结构")
                elif test_name == "professional_prompts":
                    recommendations.append("建议完善提示词模板和Few-shot示例")
                elif test_name == "deepseek_r1_reasoning":
                    recommendations.append("建议加强推理链验证和自我纠错机制")
                elif test_name == "advanced_rag":
                    recommendations.append("建议调整RAG技术权重和融合策略")
                elif test_name == "data_processing":
                    recommendations.append("建议改进数据质量评估标准和处理流程")
                elif test_name == "output_formatting":
                    recommendations.append("建议完善报告模板和格式化规则")
                elif test_name == "system_configuration":
                    recommendations.append("建议检查配置文件完整性和环境变量设置")
        
        if self.overall_score >= 0.9:
            recommendations.append("系统整体表现优秀，建议进行生产环境部署测试")
        elif self.overall_score >= 0.7:
            recommendations.append("系统基本功能正常，建议针对低分项进行优化")
        else:
            recommendations.append("系统存在较多问题，建议全面检查和重构")
        
        return recommendations
    
    def _assess_system_status(self) -> str:
        """评估系统状态"""
        if self.overall_score >= 0.9:
            return "优秀 - 系统达到生产级标准"
        elif self.overall_score >= 0.8:
            return "良好 - 系统基本满足要求，需少量优化"
        elif self.overall_score >= 0.6:
            return "可接受 - 系统功能基本可用，需要改进"
        else:
            return "需要改进 - 系统存在重大问题，需要重构"


def main():
    """主函数"""
    print("🚀 启动白银市电力故障诊断系统集成测试")
    
    # 创建日志目录
    os.makedirs("logs", exist_ok=True)
    
    # 运行测试
    tester = SystemIntegrationTester()
    report = tester.run_all_tests()
    
    # 输出测试结果摘要
    print("\n" + "="*60)
    print("📊 测试结果摘要")
    print("="*60)
    print(f"总体分数: {report['test_summary']['overall_score']:.2f}")
    print(f"测试时间: {report['test_summary']['total_time']:.2f}秒")
    print(f"系统状态: {report['system_status']}")
    
    print("\n📋 各模块分数:")
    for test_name, result in report['test_results'].items():
        score = result.get('score', 0.0)
        status = "✅" if score >= 0.7 else "⚠️" if score >= 0.5 else "❌"
        print(f"  {status} {test_name}: {score:.2f}")
    
    print("\n💡 改进建议:")
    for i, recommendation in enumerate(report['recommendations'], 1):
        print(f"  {i}. {recommendation}")
    
    print("\n🎉 测试完成！")
    return report


if __name__ == "__main__":
    main()
