#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化数据处理器
提供稳定可靠的数据处理功能，确保系统正常运行
"""

import re
import time
import logging
from typing import Dict, List, Any
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class DocumentType(Enum):
    """文档类型"""
    FAULT_REPORT = "fault_report"
    MAINTENANCE_GUIDE = "maintenance_guide"
    TECHNICAL_MANUAL = "technical_manual"
    OPERATION_LOG = "operation_log"
    UNKNOWN = "unknown"


@dataclass
class DataQualityMetrics:
    """数据质量指标"""
    completeness: float = 0.0
    accuracy: float = 0.0
    consistency: float = 0.0
    timeliness: float = 0.0
    validity: float = 0.0
    uniqueness: float = 0.0
    professional_depth: float = 0.0
    structural_quality: float = 0.0
    overall_score: float = 0.0


@dataclass
class DocumentAnnotation:
    """文档标注结果"""
    document_id: str
    document_type: DocumentType
    entities: List[Dict[str, Any]]
    relationships: List[Dict[str, Any]]
    technical_terms: List[str]
    key_concepts: List[str]
    quality_metrics: DataQualityMetrics
    metadata: Dict[str, Any]
    annotation_timestamp: float


class SimpleDataProcessor:
    """简化数据处理器"""
    
    def __init__(self):
        # 电力系统专业术语库
        self.technical_terms = {
            "设备类型": ["变压器", "断路器", "线路", "母线", "保护装置", "互感器"],
            "故障类型": ["短路", "接地", "过流", "过压", "绝缘", "击穿", "闪络"],
            "技术参数": ["电压", "电流", "功率", "频率", "温度", "压力"],
            "操作术语": ["投运", "停运", "检修", "试验", "校验", "维护"]
        }
        
        # 实体识别模式
        self.entity_patterns = {
            "电压": r'(\d+(?:\.\d+)?)\s*[kK]?[vV]',
            "电流": r'(\d+(?:\.\d+)?)\s*[kK]?[aA]',
            "功率": r'(\d+(?:\.\d+)?)\s*[mMkK]?[wWvV][aA]?',
            "温度": r'(\d+(?:\.\d+)?)\s*[℃°][cC]?',
            "时间": r'(\d{1,2}:\d{2}|\d{4}-\d{2}-\d{2})'
        }
    
    def process_document(self, content: str, metadata: Dict[str, Any] = None) -> DocumentAnnotation:
        """处理文档"""
        try:
            if metadata is None:
                metadata = {}
            
            doc_id = metadata.get("id", f"doc_{int(time.time())}")
            
            # 1. 文档类型识别
            doc_type = self._classify_document_type(content)
            
            # 2. 实体识别
            entities = self._extract_entities(content)
            
            # 3. 技术术语提取
            technical_terms = self._extract_technical_terms(content)
            
            # 4. 关键概念提取
            key_concepts = self._extract_key_concepts(content)
            
            # 5. 关系抽取（简化版）
            relationships = self._extract_simple_relationships(content, entities)
            
            # 6. 数据质量评估
            quality_metrics = self._assess_data_quality(content, entities, technical_terms)
            
            # 构建标注结果
            annotation = DocumentAnnotation(
                document_id=doc_id,
                document_type=doc_type,
                entities=entities,
                relationships=relationships,
                technical_terms=technical_terms,
                key_concepts=key_concepts,
                quality_metrics=quality_metrics,
                metadata=metadata,
                annotation_timestamp=time.time()
            )
            
            logger.info(f"文档 {doc_id} 处理完成，质量分数: {quality_metrics.overall_score:.2f}")
            return annotation
            
        except Exception as e:
            logger.error(f"文档处理失败: {e}")
            
            # 返回基础标注
            return DocumentAnnotation(
                document_id=metadata.get("id", f"doc_{int(time.time())}") if metadata else f"doc_{int(time.time())}",
                document_type=DocumentType.UNKNOWN,
                entities=[],
                relationships=[],
                technical_terms=[],
                key_concepts=[],
                quality_metrics=DataQualityMetrics(
                    completeness=0.5, accuracy=0.5, consistency=0.5,
                    timeliness=0.5, validity=0.5, uniqueness=0.5,
                    professional_depth=0.5, structural_quality=0.5,
                    overall_score=0.5
                ),
                metadata=metadata or {},
                annotation_timestamp=time.time()
            )
    
    def _classify_document_type(self, content: str) -> DocumentType:
        """文档类型分类"""
        content_lower = content.lower()
        
        # 基于关键词的分类
        if any(keyword in content_lower for keyword in ["故障", "事故", "跳闸", "异常"]):
            return DocumentType.FAULT_REPORT
        elif any(keyword in content_lower for keyword in ["检修", "维护", "保养", "试验"]):
            return DocumentType.MAINTENANCE_GUIDE
        elif any(keyword in content_lower for keyword in ["技术", "规范", "标准", "手册"]):
            return DocumentType.TECHNICAL_MANUAL
        elif any(keyword in content_lower for keyword in ["运行", "操作", "记录", "日志"]):
            return DocumentType.OPERATION_LOG
        else:
            return DocumentType.UNKNOWN
    
    def _extract_entities(self, content: str) -> List[Dict[str, Any]]:
        """实体识别"""
        entities = []
        
        # 使用正则表达式提取数值实体
        for entity_type, pattern in self.entity_patterns.items():
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                entities.append({
                    "text": match,
                    "type": entity_type,
                    "confidence": 0.8
                })
        
        # 提取设备名称
        equipment_keywords = ["变压器", "断路器", "线路", "母线", "开关"]
        for keyword in equipment_keywords:
            if keyword in content:
                entities.append({
                    "text": keyword,
                    "type": "设备",
                    "confidence": 0.9
                })
        
        return entities
    
    def _extract_technical_terms(self, content: str) -> List[str]:
        """提取技术术语"""
        technical_terms = []
        content_lower = content.lower()
        
        # 遍历术语库
        for category, terms in self.technical_terms.items():
            for term in terms:
                if term in content_lower:
                    technical_terms.append(term)
        
        # 去重
        return list(set(technical_terms))
    
    def _extract_key_concepts(self, content: str) -> List[str]:
        """提取关键概念"""
        key_concepts = []
        content_lower = content.lower()
        
        # 关键概念模式
        concept_patterns = {
            "故障分析": ["故障", "分析"],
            "技术诊断": ["诊断", "检测"],
            "维护管理": ["维护", "管理"],
            "安全操作": ["安全", "操作"],
            "质量控制": ["质量", "控制"]
        }
        
        for concept, keywords in concept_patterns.items():
            if all(keyword in content_lower for keyword in keywords):
                key_concepts.append(concept)
        
        return key_concepts
    
    def _extract_simple_relationships(self, content: str, entities: List[Dict]) -> List[Dict[str, Any]]:
        """简化的关系抽取"""
        relationships = []
        
        # 简单的因果关系识别
        if "导致" in content or "引起" in content or "造成" in content:
            relationships.append({
                "type": "因果关系",
                "description": "文档中包含因果关系描述",
                "confidence": 0.7
            })
        
        # 设备与故障的关系
        equipment_entities = [e for e in entities if e.get("type") == "设备"]
        if equipment_entities and any(keyword in content for keyword in ["故障", "异常", "问题"]):
            relationships.append({
                "type": "设备故障关系",
                "description": "设备与故障现象的关联",
                "confidence": 0.8
            })
        
        return relationships
    
    def _assess_data_quality(self, content: str, entities: List[Dict], technical_terms: List[str]) -> DataQualityMetrics:
        """数据质量评估"""
        
        # 1. 完整性评估
        completeness = self._assess_completeness(content, entities, technical_terms)
        
        # 2. 准确性评估
        accuracy = self._assess_accuracy(content, technical_terms)
        
        # 3. 一致性评估
        consistency = self._assess_consistency(content, technical_terms)
        
        # 4. 时效性评估
        timeliness = 0.8  # 简化处理
        
        # 5. 有效性评估
        validity = self._assess_validity(content, entities)
        
        # 6. 唯一性评估
        uniqueness = 0.9  # 简化处理
        
        # 7. 专业深度评估
        professional_depth = self._assess_professional_depth(content, technical_terms)
        
        # 8. 结构质量评估
        structural_quality = self._assess_structural_quality(content)
        
        # 计算总体分数
        weights = {
            "completeness": 0.15,
            "accuracy": 0.20,
            "consistency": 0.15,
            "timeliness": 0.10,
            "validity": 0.15,
            "uniqueness": 0.10,
            "professional_depth": 0.10,
            "structural_quality": 0.05
        }
        
        overall_score = (
            completeness * weights["completeness"] +
            accuracy * weights["accuracy"] +
            consistency * weights["consistency"] +
            timeliness * weights["timeliness"] +
            validity * weights["validity"] +
            uniqueness * weights["uniqueness"] +
            professional_depth * weights["professional_depth"] +
            structural_quality * weights["structural_quality"]
        )
        
        return DataQualityMetrics(
            completeness=completeness,
            accuracy=accuracy,
            consistency=consistency,
            timeliness=timeliness,
            validity=validity,
            uniqueness=uniqueness,
            professional_depth=professional_depth,
            structural_quality=structural_quality,
            overall_score=min(overall_score, 1.0)
        )
    
    def _assess_completeness(self, content: str, entities: List[Dict], technical_terms: List[str]) -> float:
        """评估完整性"""
        score = 0.0
        
        # 基于内容长度
        length_score = min(len(content) / 300, 1.0) * 0.4
        score += length_score
        
        # 基于实体数量
        entity_score = min(len(entities) / 3, 1.0) * 0.3
        score += entity_score
        
        # 基于技术术语数量
        term_score = min(len(technical_terms) / 3, 1.0) * 0.3
        score += term_score
        
        return min(score, 1.0)
    
    def _assess_accuracy(self, content: str, technical_terms: List[str]) -> float:
        """评估准确性"""
        score = 0.0
        
        # 基于技术术语密度
        if len(content.split()) > 0:
            term_density = len(technical_terms) / len(content.split())
            score += min(term_density * 10, 1.0) * 0.6
        
        # 基于专业指标
        professional_indicators = ["技术", "分析", "标准", "规范"]
        indicator_count = sum(1 for indicator in professional_indicators if indicator in content)
        score += min(indicator_count / 3, 1.0) * 0.4
        
        return min(score, 1.0)
    
    def _assess_consistency(self, content: str, technical_terms: List[str]) -> float:
        """评估一致性"""
        # 简化处理，基于术语使用的一致性
        if not technical_terms:
            return 0.6
        
        # 检查术语重复使用
        unique_terms = set(technical_terms)
        consistency_score = len(unique_terms) / len(technical_terms) if technical_terms else 0.6
        
        return min(consistency_score + 0.3, 1.0)
    
    def _assess_validity(self, content: str, entities: List[Dict]) -> float:
        """评估有效性"""
        score = 0.7  # 基础分数
        
        # 检查数值实体的合理性
        numeric_entities = [e for e in entities if e.get("type") in ["电压", "电流", "功率", "温度"]]
        if numeric_entities:
            score += 0.2
        
        return min(score, 1.0)
    
    def _assess_professional_depth(self, content: str, technical_terms: List[str]) -> float:
        """评估专业深度"""
        score = 0.0
        
        # 基于技术术语丰富度
        term_score = min(len(technical_terms) / 5, 1.0) * 0.5
        score += term_score
        
        # 基于专业概念
        depth_indicators = ["机理", "原理", "分析", "诊断", "评估"]
        depth_count = sum(1 for indicator in depth_indicators if indicator in content)
        depth_score = min(depth_count / 3, 1.0) * 0.5
        score += depth_score
        
        return min(score, 1.0)
    
    def _assess_structural_quality(self, content: str) -> float:
        """评估结构质量"""
        score = 0.0
        
        # 检查结构化元素
        structure_indicators = ["1.", "2.", "3.", "##", "**", "：", "。"]
        structure_count = sum(1 for indicator in structure_indicators if indicator in content)
        score = min(structure_count / 5, 1.0)
        
        return score


# 创建全局实例
simple_data_processor = SimpleDataProcessor()
