
import sys
import importlib
from pathlib import Path

def check_system_status():
    """检查系统状态"""
    status = {
        "modules": {},
        "files": {},
        "overall": "unknown"
    }
    
    # 检查关键模块
    modules_to_check = [
        "retriever.unified_professional_retriever",
        "langchain_modules.prompts.professional_prompt_templates", 
        "langchain_modules.reasoning.deepseek_r1_optimizer",
        "data_processing.intelligent_data_processor",
        "output_formatting.professional_report_formatter"
    ]
    
    for module_name in modules_to_check:
        try:
            importlib.import_module(module_name)
            status["modules"][module_name] = "available"
        except ImportError as e:
            status["modules"][module_name] = f"missing: {e}"
        except Exception as e:
            status["modules"][module_name] = f"error: {e}"
    
    # 检查关键文件
    files_to_check = [
        "knowledge_base/text",
        "configs",
        "logs"
    ]
    
    for file_path in files_to_check:
        path = Path(file_path)
        if path.exists():
            status["files"][file_path] = "exists"
        else:
            status["files"][file_path] = "missing"
    
    # 评估整体状态
    available_modules = sum(1 for s in status["modules"].values() if s == "available")
    total_modules = len(status["modules"])
    
    if available_modules >= total_modules * 0.8:
        status["overall"] = "good"
    elif available_modules >= total_modules * 0.6:
        status["overall"] = "acceptable"
    else:
        status["overall"] = "needs_improvement"
    
    return status

if __name__ == "__main__":
    status = check_system_status()
    print("System Status Check:")
    print(f"Overall: {status['overall']}")
    print("\nModules:")
    for module, status_info in status["modules"].items():
        print(f"  {module}: {status_info}")
    print("\nFiles:")
    for file_path, status_info in status["files"].items():
        print(f"  {file_path}: {status_info}")
