#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
UI配置管理模块
提取ui/app.py中的配置相关代码，保持功能完整
"""

import os
from typing import Dict, Any

class UIConfig:
    """UI配置管理类"""
    
    def __init__(self):
        """初始化配置"""
        self.load_config()
    
    def load_config(self):
        """加载配置"""
        # DeepSeek API配置 - 优先从环境变量读取，保持向后兼容
        self.DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "***********************************")
        self.DEEPSEEK_API_BASE = os.getenv("DEEPSEEK_API_BASE", "https://dashscope.aliyuncs.com/compatible-mode/v1")
        self.DEEPSEEK_API_ENDPOINT = "/chat/completions"
        self.DEEPSEEK_R1_MODEL = "deepseek-r1"
        self.DEEPSEEK_CHAT_MODEL = "deepseek-v3"
        
        # 服务器配置
        self.HOST = os.getenv('HOST', '0.0.0.0')
        self.PORT = int(os.getenv('PORT', '5002'))
        self.DEBUG = os.getenv('DEBUG', 'false').lower() == 'true'
        
        # 文件上传配置
        self.MAX_FILE_SIZE = int(os.getenv('MAX_FILE_SIZE', '100'))  # MB
        self.UPLOAD_PATH = os.getenv('UPLOAD_PATH', './data/01_raw/uploads')
        
        # 安全配置
        self.SECRET_KEY = os.getenv('SECRET_KEY', 'fault-diagnosis-secret-key-2025')
        self.CSRF_PROTECTION = os.getenv('CSRF_PROTECTION', 'true').lower() == 'true'
        self.RATE_LIMITING = os.getenv('RATE_LIMITING', 'true').lower() == 'true'
        self.MAX_REQUESTS_PER_MINUTE = int(os.getenv('MAX_REQUESTS_PER_MINUTE', '100'))
        
        # 功能开关
        self.ENABLE_MULTIMODAL_SEARCH = os.getenv('ENABLE_MULTIMODAL_SEARCH', 'true').lower() == 'true'
        self.ENABLE_AUTO_KNOWLEDGE_UPDATE = os.getenv('ENABLE_AUTO_KNOWLEDGE_UPDATE', 'true').lower() == 'true'
        self.ENABLE_REAL_TIME_SYNC = os.getenv('ENABLE_REAL_TIME_SYNC', 'false').lower() == 'true'
        self.ENABLE_GPU_ACCELERATION = os.getenv('ENABLE_GPU_ACCELERATION', 'false').lower() == 'true'
        
        # 监控配置
        self.ENABLE_METRICS = os.getenv('ENABLE_METRICS', 'true').lower() == 'true'
        self.METRICS_PORT = int(os.getenv('METRICS_PORT', '9090'))
        self.HEALTH_CHECK_INTERVAL = int(os.getenv('HEALTH_CHECK_INTERVAL', '30'))
    
    def get_deepseek_config(self) -> Dict[str, Any]:
        """获取DeepSeek配置"""
        return {
            'api_key': self.DEEPSEEK_API_KEY,
            'base_url': self.DEEPSEEK_API_BASE,
            'endpoint': self.DEEPSEEK_API_ENDPOINT,
            'r1_model': self.DEEPSEEK_R1_MODEL,
            'chat_model': self.DEEPSEEK_CHAT_MODEL
        }
    
    def get_server_config(self) -> Dict[str, Any]:
        """获取服务器配置"""
        return {
            'host': self.HOST,
            'port': self.PORT,
            'debug': self.DEBUG
        }
    
    def get_upload_config(self) -> Dict[str, Any]:
        """获取上传配置"""
        return {
            'max_file_size': self.MAX_FILE_SIZE,
            'upload_path': self.UPLOAD_PATH
        }
    
    def get_security_config(self) -> Dict[str, Any]:
        """获取安全配置"""
        return {
            'secret_key': self.SECRET_KEY,
            'csrf_protection': self.CSRF_PROTECTION,
            'rate_limiting': self.RATE_LIMITING,
            'max_requests_per_minute': self.MAX_REQUESTS_PER_MINUTE
        }
    
    def get_feature_flags(self) -> Dict[str, bool]:
        """获取功能开关"""
        return {
            'multimodal_search': self.ENABLE_MULTIMODAL_SEARCH,
            'auto_knowledge_update': self.ENABLE_AUTO_KNOWLEDGE_UPDATE,
            'real_time_sync': self.ENABLE_REAL_TIME_SYNC,
            'gpu_acceleration': self.ENABLE_GPU_ACCELERATION
        }
    
    def get_monitoring_config(self) -> Dict[str, Any]:
        """获取监控配置"""
        return {
            'enable_metrics': self.ENABLE_METRICS,
            'metrics_port': self.METRICS_PORT,
            'health_check_interval': self.HEALTH_CHECK_INTERVAL
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'deepseek': self.get_deepseek_config(),
            'server': self.get_server_config(),
            'upload': self.get_upload_config(),
            'security': self.get_security_config(),
            'features': self.get_feature_flags(),
            'monitoring': self.get_monitoring_config()
        }

# 创建全局配置实例
ui_config = UIConfig()

# 为了保持向后兼容，导出常用配置
DEEPSEEK_API_KEY = ui_config.DEEPSEEK_API_KEY
DEEPSEEK_API_BASE = ui_config.DEEPSEEK_API_BASE
DEEPSEEK_API_ENDPOINT = ui_config.DEEPSEEK_API_ENDPOINT
DEEPSEEK_R1_MODEL = ui_config.DEEPSEEK_R1_MODEL
DEEPSEEK_CHAT_MODEL = ui_config.DEEPSEEK_CHAT_MODEL
