#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek客户端模块
提取ui/app.py中的DeepSeek相关代码，保持功能完整
"""

import json
import requests
import time
from typing import Dict, List, Any, Optional
import logging

logger = logging.getLogger(__name__)

class DeepSeekClient:
    """DeepSeek API客户端"""

    def __init__(self, api_key: str, base_url: str = "https://dashscope.aliyuncs.com/compatible-mode/v1"):
        self.base_url = base_url
        self.headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json",
            "User-Agent": "PowerSystem-FaultAnalysis/1.0"
        }

        # 阿里云DashScope特殊配置
        if "dashscope.aliyuncs.com" in base_url:
            self.headers["X-DashScope-SSE"] = "enable"

    def chat_completion(self, messages: list, model: str = "deepseek-v3",
                       temperature: float = 0.7, max_tokens: int = 2000, max_retries: int = 2,
                       stream: bool = False):
        """阿里云DashScope API调用，支持流式和非流式响应"""
        url = f"{self.base_url}/chat/completions"

        # 阿里云DashScope API payload格式
        payload = {
            "model": model,
            "messages": messages,
            "temperature": temperature,
            "max_tokens": max_tokens,
            "stream": stream
        }

        # DeepSeek-R1特殊配置
        if model == "deepseek-r1":
            payload["reasoning"] = True

        for attempt in range(max_retries + 1):
            try:
                if stream:
                    response = requests.post(url, headers=self.headers, json=payload, stream=True, timeout=120)
                else:
                    response = requests.post(url, headers=self.headers, json=payload, timeout=60)

                if response.status_code == 200:
                    if stream:
                        return response
                    else:
                        return response.json()
                else:
                    error_msg = f"API调用失败: {response.status_code}"
                    try:
                        error_detail = response.json()
                        error_msg += f" - {error_detail}"
                    except:
                        error_msg += f" - {response.text}"
                    
                    logger.error(error_msg)
                    
                    if attempt < max_retries:
                        wait_time = 2 ** attempt
                        logger.info(f"等待 {wait_time} 秒后重试...")
                        time.sleep(wait_time)
                        continue
                    else:
                        return {"error": error_msg}

            except requests.exceptions.Timeout:
                error_msg = f"请求超时 (尝试 {attempt + 1}/{max_retries + 1})"
                logger.error(error_msg)
                
                if attempt < max_retries:
                    wait_time = 2 ** attempt
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                    continue
                else:
                    return {"error": "请求超时，请稍后重试"}

            except Exception as e:
                error_msg = f"请求异常: {str(e)} (尝试 {attempt + 1}/{max_retries + 1})"
                logger.error(error_msg)
                
                if attempt < max_retries:
                    wait_time = 2 ** attempt
                    logger.info(f"等待 {wait_time} 秒后重试...")
                    time.sleep(wait_time)
                    continue
                else:
                    return {"error": f"请求异常: {str(e)}"}

        return {"error": "所有重试都失败了"}

    def _make_stream_request(self, messages: List[Dict], model: str, temperature: float = 0.7, max_tokens: int = 2000):
        """创建流式请求"""
        try:
            return self.chat_completion(
                messages=messages,
                model=model,
                temperature=temperature,
                max_tokens=max_tokens,
                stream=True
            )
        except Exception as e:
            logger.error(f"创建流式请求失败: {e}")
            return None

    def test_connection(self) -> Dict[str, Any]:
        """测试API连接"""
        try:
            test_messages = [{"role": "user", "content": "你好"}]
            result = self.chat_completion(
                messages=test_messages,
                model="deepseek-v3",
                max_tokens=50,
                stream=False
            )
            
            if "error" in result:
                return {"success": False, "error": result["error"]}
            else:
                return {"success": True, "message": "连接正常"}
                
        except Exception as e:
            return {"success": False, "error": f"连接测试失败: {str(e)}"}

class DeepSeekR1Config:
    """DeepSeek-R1配置管理"""
    
    # R1模型的不同配置类型
    TYPE_CONFIGS = {
        "balanced": {
            "temperature": 0.3,
            "max_tokens": 8192,
            "description": "平衡模式 - 兼顾准确性和创造性"
        },
        "precise": {
            "temperature": 0.1,
            "max_tokens": 12288,
            "description": "精确模式 - 最高准确性，适合技术分析"
        },
        "creative": {
            "temperature": 0.5,
            "max_tokens": 6144,
            "description": "创新模式 - 更多创造性思维"
        },
        "detailed": {
            "temperature": 0.2,
            "max_tokens": 16384,
            "description": "详细模式 - 最详细的分析过程"
        }
    }
    
    @classmethod
    def get_config(cls, config_type: str = "balanced") -> Dict[str, Any]:
        """获取指定类型的配置"""
        return cls.TYPE_CONFIGS.get(config_type, cls.TYPE_CONFIGS["balanced"])
    
    @classmethod
    def get_all_configs(cls) -> Dict[str, Dict[str, Any]]:
        """获取所有配置"""
        return cls.TYPE_CONFIGS

def create_deepseek_client(api_key: str, base_url: str = None) -> DeepSeekClient:
    """创建DeepSeek客户端的便捷函数"""
    if base_url is None:
        base_url = "https://dashscope.aliyuncs.com/compatible-mode/v1"
    
    return DeepSeekClient(api_key, base_url)
