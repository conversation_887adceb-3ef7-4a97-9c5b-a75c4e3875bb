#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能数据处理器 - 白银市电力故障诊断系统
实现自动化数据质量评估、智能标注和专业术语标准化

核心功能：
1. 自动化数据质量评估
2. 智能数据标注和分类
3. 专业术语标准化
4. 数据结构化增强
5. 知识图谱构建
6. 数据增强和扩充
"""

import json
import logging
import re
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import numpy as np
from pathlib import Path

logger = logging.getLogger(__name__)


class DataQualityLevel(Enum):
    """数据质量等级"""
    EXCELLENT = "excellent"    # 优秀 (>0.9)
    GOOD = "good"             # 良好 (0.7-0.9)
    ACCEPTABLE = "acceptable"  # 可接受 (0.5-0.7)
    POOR = "poor"             # 较差 (<0.5)


class DocumentType(Enum):
    """文档类型"""
    FAULT_REPORT = "fault_report"        # 故障报告
    EQUIPMENT_MANUAL = "equipment_manual" # 设备手册
    MAINTENANCE_LOG = "maintenance_log"   # 维护日志
    TECHNICAL_SPEC = "technical_spec"     # 技术规格
    SAFETY_PROTOCOL = "safety_protocol"  # 安全协议
    CASE_STUDY = "case_study"            # 案例研究
    UNKNOWN = "unknown"                  # 未知类型


@dataclass
class DataQualityMetrics:
    """数据质量指标"""
    completeness: float          # 完整性
    accuracy: float             # 准确性
    consistency: float          # 一致性
    timeliness: float          # 时效性
    validity: float            # 有效性
    uniqueness: float          # 唯一性
    professional_depth: float  # 专业深度
    structural_quality: float  # 结构质量
    overall_score: float       # 总体分数
    quality_level: DataQualityLevel


@dataclass
class DocumentAnnotation:
    """文档标注"""
    document_id: str
    document_type: DocumentType
    entities: List[Dict[str, Any]]      # 实体识别结果
    relationships: List[Dict[str, Any]]  # 关系抽取结果
    technical_terms: List[str]          # 技术术语
    key_concepts: List[str]             # 关键概念
    quality_metrics: DataQualityMetrics
    metadata: Dict[str, Any]
    annotation_timestamp: float


class PowerSystemKnowledgeBase:
    """电力系统知识库"""
    
    def __init__(self):
        # 专业术语标准化词典
        self.terminology_dict = {
            # 设备类型标准化
            "equipment_types": {
                "变压器": {
                    "standard_term": "变压器",
                    "aliases": ["变电器", "电力变压器", "主变", "配变"],
                    "category": "主设备",
                    "description": "改变交流电压等级的静止电器"
                },
                "断路器": {
                    "standard_term": "断路器", 
                    "aliases": ["开关", "高压开关", "SF6断路器", "真空断路器"],
                    "category": "开关设备",
                    "description": "能够关合、承载和开断正常回路条件下的电流"
                },
                "隔离开关": {
                    "standard_term": "隔离开关",
                    "aliases": ["刀闸", "隔离刀闸", "接地开关"],
                    "category": "开关设备", 
                    "description": "在分位置时提供可见断开点的开关设备"
                }
            },
            
            # 故障类型标准化
            "fault_types": {
                "短路故障": {
                    "standard_term": "短路故障",
                    "aliases": ["短路", "接地短路", "相间短路", "三相短路"],
                    "category": "电气故障",
                    "severity": "高"
                },
                "绝缘故障": {
                    "standard_term": "绝缘故障", 
                    "aliases": ["绝缘击穿", "绝缘老化", "绝缘下降"],
                    "category": "绝缘故障",
                    "severity": "中高"
                },
                "机械故障": {
                    "standard_term": "机械故障",
                    "aliases": ["机械卡涩", "机械变形", "机械磨损"],
                    "category": "机械故障", 
                    "severity": "中"
                }
            },
            
            # 技术参数标准化
            "technical_parameters": {
                "额定电压": {
                    "standard_term": "额定电压",
                    "aliases": ["电压等级", "工作电压", "运行电压"],
                    "unit": "kV",
                    "category": "电气参数"
                },
                "额定电流": {
                    "standard_term": "额定电流",
                    "aliases": ["工作电流", "负载电流", "运行电流"],
                    "unit": "A", 
                    "category": "电气参数"
                },
                "绝缘电阻": {
                    "standard_term": "绝缘电阻",
                    "aliases": ["绝缘阻值", "绝缘水平"],
                    "unit": "MΩ",
                    "category": "绝缘参数"
                }
            }
        }
        
        # 实体识别模式
        self.entity_patterns = {
            "voltage": r"(\d+(?:\.\d+)?)\s*[kK][Vv]",
            "current": r"(\d+(?:\.\d+)?)\s*[Aa]",
            "power": r"(\d+(?:\.\d+)?)\s*[MmKk]?[WwVvAa]",
            "temperature": r"(\d+(?:\.\d+)?)\s*[°℃]?[Cc]",
            "time": r"(\d{4}[-/]\d{1,2}[-/]\d{1,2})",
            "equipment_id": r"[A-Z]{1,3}\d{2,4}[A-Z]?"
        }
        
        # 关系抽取模式
        self.relationship_patterns = {
            "cause_effect": [
                r"(.+?)导致(.+)",
                r"(.+?)引起(.+)", 
                r"由于(.+?)，(.+)",
                r"因为(.+?)，所以(.+)"
            ],
            "equipment_fault": [
                r"(.+?)(发生|出现)(.+?)(故障|异常)",
                r"(.+?)(故障|异常)(.+)"
            ],
            "parameter_value": [
                r"(.+?)(为|是|达到)(.+)",
                r"(.+?)(测量|检测|监测)(.+)"
            ]
        }


class IntelligentDataProcessor:
    """智能数据处理器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.knowledge_base = PowerSystemKnowledgeBase()
        
        # 质量评估权重
        self.quality_weights = {
            "completeness": 0.15,
            "accuracy": 0.20,
            "consistency": 0.15,
            "timeliness": 0.10,
            "validity": 0.15,
            "uniqueness": 0.10,
            "professional_depth": 0.10,
            "structural_quality": 0.05
        }
    
    def process_document(self, content: str, metadata: Dict[str, Any] = None) -> DocumentAnnotation:
        """处理单个文档"""
        try:
            doc_id = metadata.get("id", f"doc_{int(time.time())}")
            
            # 1. 文档类型识别
            doc_type = self._classify_document_type(content)
            
            # 2. 实体识别
            entities = self._extract_entities(content)
            
            # 3. 关系抽取
            relationships = self._extract_relationships(content)
            
            # 4. 技术术语提取和标准化
            technical_terms = self._extract_and_standardize_terms(content)
            
            # 5. 关键概念提取
            key_concepts = self._extract_key_concepts(content)
            
            # 6. 数据质量评估
            quality_metrics = self._assess_data_quality(content, entities, relationships, technical_terms)
            
            # 7. 构建标注结果
            annotation = DocumentAnnotation(
                document_id=doc_id,
                document_type=doc_type,
                entities=entities,
                relationships=relationships,
                technical_terms=technical_terms,
                key_concepts=key_concepts,
                quality_metrics=quality_metrics,
                metadata=metadata or {},
                annotation_timestamp=time.time()
            )
            
            logger.info(f"文档 {doc_id} 处理完成，质量分数: {quality_metrics.overall_score:.2f}")
            
            return annotation
            
        except Exception as e:
            logger.error(f"文档处理失败: {e}")
            # 返回基础标注
            return DocumentAnnotation(
                document_id=doc_id,
                document_type=DocumentType.UNKNOWN,
                entities=[],
                relationships=[],
                technical_terms=[],
                key_concepts=[],
                quality_metrics=DataQualityMetrics(
                    completeness=0.0, accuracy=0.0, consistency=0.0,
                    timeliness=0.0, validity=0.0, uniqueness=0.0,
                    professional_depth=0.0, structural_quality=0.0,
                    overall_score=0.0, quality_level=DataQualityLevel.POOR
                ),
                metadata={"error": str(e)},
                annotation_timestamp=time.time()
            )
    
    def _classify_document_type(self, content: str) -> DocumentType:
        """文档类型分类"""
        content_lower = content.lower()
        
        # 基于关键词的简单分类
        type_indicators = {
            DocumentType.FAULT_REPORT: ["故障", "异常", "跳闸", "报警", "事故"],
            DocumentType.EQUIPMENT_MANUAL: ["手册", "说明书", "技术资料", "操作指南"],
            DocumentType.MAINTENANCE_LOG: ["检修", "维护", "保养", "巡检", "试验"],
            DocumentType.TECHNICAL_SPEC: ["技术规格", "参数", "性能", "指标", "标准"],
            DocumentType.SAFETY_PROTOCOL: ["安全", "规程", "制度", "措施", "防护"],
            DocumentType.CASE_STUDY: ["案例", "分析", "经验", "总结", "研究"]
        }
        
        max_score = 0
        best_type = DocumentType.UNKNOWN
        
        for doc_type, keywords in type_indicators.items():
            score = sum(1 for keyword in keywords if keyword in content_lower)
            if score > max_score:
                max_score = score
                best_type = doc_type
        
        return best_type
    
    def _extract_entities(self, content: str) -> List[Dict[str, Any]]:
        """实体识别"""
        entities = []
        
        for entity_type, pattern in self.knowledge_base.entity_patterns.items():
            matches = re.finditer(pattern, content)
            for match in matches:
                entity = {
                    "type": entity_type,
                    "value": match.group(1) if match.groups() else match.group(0),
                    "start": match.start(),
                    "end": match.end(),
                    "confidence": 0.8
                }
                entities.append(entity)
        
        return entities
    
    def _extract_relationships(self, content: str) -> List[Dict[str, Any]]:
        """关系抽取"""
        relationships = []
        
        for relation_type, patterns in self.knowledge_base.relationship_patterns.items():
            for pattern in patterns:
                matches = re.finditer(pattern, content)
                for match in matches:
                    if len(match.groups()) >= 2:
                        relationship = {
                            "type": relation_type,
                            "subject": match.group(1).strip(),
                            "object": match.group(2).strip(),
                            "confidence": 0.7,
                            "context": match.group(0)
                        }
                        relationships.append(relationship)
        
        return relationships
    
    def _extract_and_standardize_terms(self, content: str) -> List[str]:
        """提取和标准化技术术语"""
        standardized_terms = []
        content_lower = content.lower()
        
        # 遍历所有术语类别
        for category, terms_dict in self.knowledge_base.terminology_dict.items():
            for standard_term, term_info in terms_dict.items():
                # 检查标准术语
                if standard_term in content:
                    standardized_terms.append(standard_term)
                
                # 检查别名
                for alias in term_info.get("aliases", []):
                    if alias in content:
                        standardized_terms.append(standard_term)  # 使用标准术语
        
        return list(set(standardized_terms))
    
    def _extract_key_concepts(self, content: str) -> List[str]:
        """提取关键概念"""
        # 基于词频和专业术语权重提取关键概念
        words = re.findall(r'\b\w+\b', content.lower())
        word_freq = {}
        
        for word in words:
            if len(word) > 2:  # 过滤短词
                word_freq[word] = word_freq.get(word, 0) + 1
        
        # 按频率排序，取前10个
        key_concepts = sorted(word_freq.items(), key=lambda x: x[1], reverse=True)[:10]
        
        return [concept[0] for concept in key_concepts]
    
    def _assess_data_quality(self, content: str, entities: List[Dict], 
                           relationships: List[Dict], technical_terms: List[str]) -> DataQualityMetrics:
        """评估数据质量"""
        
        # 1. 完整性评估
        completeness = self._assess_completeness(content, entities, technical_terms)
        
        # 2. 准确性评估
        accuracy = self._assess_accuracy(content, entities, technical_terms)
        
        # 3. 一致性评估
        consistency = self._assess_consistency(content, technical_terms)
        
        # 4. 时效性评估
        timeliness = self._assess_timeliness(content)
        
        # 5. 有效性评估
        validity = self._assess_validity(content, entities)
        
        # 6. 唯一性评估
        uniqueness = self._assess_uniqueness(content)
        
        # 7. 专业深度评估
        professional_depth = self._assess_professional_depth(content, technical_terms, relationships)
        
        # 8. 结构质量评估
        structural_quality = self._assess_structural_quality(content)
        
        # 计算总体分数
        overall_score = (
            completeness * self.quality_weights["completeness"] +
            accuracy * self.quality_weights["accuracy"] +
            consistency * self.quality_weights["consistency"] +
            timeliness * self.quality_weights["timeliness"] +
            validity * self.quality_weights["validity"] +
            uniqueness * self.quality_weights["uniqueness"] +
            professional_depth * self.quality_weights["professional_depth"] +
            structural_quality * self.quality_weights["structural_quality"]
        )
        
        # 确定质量等级
        if overall_score >= 0.9:
            quality_level = DataQualityLevel.EXCELLENT
        elif overall_score >= 0.7:
            quality_level = DataQualityLevel.GOOD
        elif overall_score >= 0.5:
            quality_level = DataQualityLevel.ACCEPTABLE
        else:
            quality_level = DataQualityLevel.POOR
        
        return DataQualityMetrics(
            completeness=completeness,
            accuracy=accuracy,
            consistency=consistency,
            timeliness=timeliness,
            validity=validity,
            uniqueness=uniqueness,
            professional_depth=professional_depth,
            structural_quality=structural_quality,
            overall_score=overall_score,
            quality_level=quality_level
        )
    
    def _assess_completeness(self, content: str, entities: List[Dict], technical_terms: List[str]) -> float:
        """评估完整性"""
        # 改善完整性评估标准
        length_score = min(len(content) / 500, 1.0)  # 降低长度要求
        entity_score = min(len(entities) / 5, 1.0)   # 降低实体要求
        term_score = min(len(technical_terms) / 3, 1.0)  # 降低术语要求

        # 内容结构评估
        structure_indicators = ["故障", "设备", "分析", "处理", "建议"]
        structure_count = sum(1 for indicator in structure_indicators if indicator in content)
        structure_score = min(structure_count / 3, 1.0)

        return (length_score + entity_score + term_score + structure_score) / 4
    
    def _assess_accuracy(self, content: str, entities: List[Dict], technical_terms: List[str]) -> float:
        """评估准确性"""
        # 改善准确性评估
        term_accuracy = min(len(technical_terms) / 4, 1.0)  # 降低要求

        if entities:
            entity_confidence = sum(entity.get("confidence", 0.8) for entity in entities) / len(entities)
        else:
            entity_confidence = 0.7  # 提高默认值

        # 专业内容评估
        professional_indicators = ["技术", "参数", "标准", "规范", "测试", "检修"]
        professional_count = sum(1 for indicator in professional_indicators if indicator in content)
        professional_score = min(professional_count / 3, 1.0)

        return (term_accuracy + entity_confidence + professional_score) / 3
    
    def _assess_consistency(self, content: str, technical_terms: List[str]) -> float:
        """评估一致性"""
        # 检查术语使用的一致性
        if not technical_terms:
            return 0.5
        
        # 简化实现：基于标准术语使用比例
        standard_terms_count = 0
        for term in technical_terms:
            # 检查是否为标准术语
            is_standard = any(
                term in terms_dict 
                for terms_dict in self.knowledge_base.terminology_dict.values()
            )
            if is_standard:
                standard_terms_count += 1
        
        return standard_terms_count / len(technical_terms) if technical_terms else 0.5
    
    def _assess_timeliness(self, content: str) -> float:
        """评估时效性"""
        # 基于时间信息的存在
        time_patterns = [
            r'\d{4}年',
            r'\d{4}-\d{2}-\d{2}',
            r'\d{4}/\d{2}/\d{2}',
            r'最近',
            r'当前',
            r'现在'
        ]
        
        time_indicators = sum(1 for pattern in time_patterns if re.search(pattern, content))
        return min(time_indicators / 3, 1.0)
    
    def _assess_validity(self, content: str, entities: List[Dict]) -> float:
        """评估有效性"""
        # 基于数值实体的合理性
        valid_entities = 0
        total_numeric_entities = 0
        
        for entity in entities:
            if entity["type"] in ["voltage", "current", "power", "temperature"]:
                total_numeric_entities += 1
                try:
                    value = float(entity["value"])
                    # 简单的合理性检查
                    if entity["type"] == "voltage" and 0 < value < 1000:
                        valid_entities += 1
                    elif entity["type"] == "current" and 0 < value < 10000:
                        valid_entities += 1
                    elif entity["type"] == "temperature" and -50 < value < 200:
                        valid_entities += 1
                    else:
                        valid_entities += 0.5  # 部分有效
                except ValueError:
                    pass
        
        if total_numeric_entities > 0:
            return valid_entities / total_numeric_entities
        else:
            return 0.7  # 默认值
    
    def _assess_uniqueness(self, content: str) -> float:
        """评估唯一性"""
        # 基于内容的多样性
        words = set(re.findall(r'\b\w+\b', content.lower()))
        total_words = len(re.findall(r'\b\w+\b', content.lower()))
        
        if total_words > 0:
            uniqueness = len(words) / total_words
            return min(uniqueness * 2, 1.0)  # 放大系数
        else:
            return 0.0
    
    def _assess_professional_depth(self, content: str, technical_terms: List[str], 
                                 relationships: List[Dict]) -> float:
        """评估专业深度"""
        # 基于技术术语密度和关系复杂度
        term_density = len(technical_terms) / max(len(content.split()), 1)
        relationship_complexity = min(len(relationships) / 5, 1.0)
        
        # 专业概念检查
        professional_concepts = ["分析", "诊断", "评估", "机理", "原理", "标准", "规范"]
        concept_count = sum(1 for concept in professional_concepts if concept in content)
        concept_score = min(concept_count / 5, 1.0)
        
        return (term_density * 10 + relationship_complexity + concept_score) / 3
    
    def _assess_structural_quality(self, content: str) -> float:
        """评估结构质量"""
        # 基于段落结构和格式
        paragraphs = content.split('\n\n')
        paragraph_score = min(len(paragraphs) / 5, 1.0)
        
        # 检查结构化元素
        structure_elements = ['。', '：', '；', '、', '（', '）']
        structure_count = sum(content.count(element) for element in structure_elements)
        structure_score = min(structure_count / 20, 1.0)
        
        return (paragraph_score + structure_score) / 2
    
    def enhance_document_structure(self, content: str, annotation: DocumentAnnotation) -> str:
        """增强文档结构"""
        try:
            enhanced_content = content
            
            # 1. 添加文档类型标识
            type_header = f"# 文档类型：{annotation.document_type.value}\n\n"
            enhanced_content = type_header + enhanced_content
            
            # 2. 标准化技术术语
            for term in annotation.technical_terms:
                # 查找标准术语信息
                for category, terms_dict in self.knowledge_base.terminology_dict.items():
                    if term in terms_dict:
                        term_info = terms_dict[term]
                        # 可以在这里添加术语解释或标准化
                        break
            
            # 3. 添加实体标注
            if annotation.entities:
                entity_section = "\n\n## 识别的实体\n"
                for entity in annotation.entities[:5]:  # 限制显示数量
                    entity_section += f"- {entity['type']}: {entity['value']}\n"
                enhanced_content += entity_section
            
            # 4. 添加关系信息
            if annotation.relationships:
                relation_section = "\n\n## 识别的关系\n"
                for relation in annotation.relationships[:3]:  # 限制显示数量
                    relation_section += f"- {relation['type']}: {relation['subject']} -> {relation['object']}\n"
                enhanced_content += relation_section
            
            # 5. 添加质量评估
            quality_section = f"\n\n## 数据质量评估\n"
            quality_section += f"- 总体质量：{annotation.quality_metrics.overall_score:.2f}\n"
            quality_section += f"- 质量等级：{annotation.quality_metrics.quality_level.value}\n"
            quality_section += f"- 专业深度：{annotation.quality_metrics.professional_depth:.2f}\n"
            enhanced_content += quality_section
            
            return enhanced_content
            
        except Exception as e:
            logger.error(f"文档结构增强失败: {e}")
            return content
    
    def batch_process_documents(self, documents: List[Dict[str, Any]]) -> List[DocumentAnnotation]:
        """批量处理文档"""
        annotations = []
        
        for i, doc in enumerate(documents):
            try:
                content = doc.get("content", "")
                metadata = doc.get("metadata", {})
                metadata["batch_index"] = i
                
                annotation = self.process_document(content, metadata)
                annotations.append(annotation)
                
                if (i + 1) % 10 == 0:
                    logger.info(f"已处理 {i + 1}/{len(documents)} 个文档")
                    
            except Exception as e:
                logger.error(f"批量处理第 {i} 个文档失败: {e}")
        
        logger.info(f"批量处理完成，共处理 {len(annotations)} 个文档")
        return annotations
    
    def generate_quality_report(self, annotations: List[DocumentAnnotation]) -> Dict[str, Any]:
        """生成质量报告"""
        if not annotations:
            return {"error": "没有可分析的标注数据"}
        
        # 统计质量分布
        quality_distribution = {level.value: 0 for level in DataQualityLevel}
        total_scores = []
        
        for annotation in annotations:
            quality_distribution[annotation.quality_metrics.quality_level.value] += 1
            total_scores.append(annotation.quality_metrics.overall_score)
        
        # 计算统计指标
        avg_score = np.mean(total_scores)
        std_score = np.std(total_scores)
        
        # 文档类型分布
        type_distribution = {}
        for annotation in annotations:
            doc_type = annotation.document_type.value
            type_distribution[doc_type] = type_distribution.get(doc_type, 0) + 1
        
        # 技术术语统计
        all_terms = []
        for annotation in annotations:
            all_terms.extend(annotation.technical_terms)
        
        term_frequency = {}
        for term in all_terms:
            term_frequency[term] = term_frequency.get(term, 0) + 1
        
        top_terms = sorted(term_frequency.items(), key=lambda x: x[1], reverse=True)[:10]
        
        return {
            "summary": {
                "total_documents": len(annotations),
                "average_quality_score": avg_score,
                "quality_std_deviation": std_score,
                "high_quality_ratio": (quality_distribution["excellent"] + quality_distribution["good"]) / len(annotations)
            },
            "quality_distribution": quality_distribution,
            "document_type_distribution": type_distribution,
            "top_technical_terms": top_terms,
            "recommendations": self._generate_quality_recommendations(annotations)
        }
    
    def _generate_quality_recommendations(self, annotations: List[DocumentAnnotation]) -> List[str]:
        """生成质量改进建议"""
        recommendations = []
        
        # 分析低质量文档
        low_quality_count = sum(1 for ann in annotations 
                               if ann.quality_metrics.quality_level in [DataQualityLevel.POOR, DataQualityLevel.ACCEPTABLE])
        
        if low_quality_count > len(annotations) * 0.3:
            recommendations.append("建议加强数据清洗和标准化流程")
        
        # 分析专业深度
        avg_professional_depth = np.mean([ann.quality_metrics.professional_depth for ann in annotations])
        if avg_professional_depth < 0.6:
            recommendations.append("建议增加专业术语和技术细节")
        
        # 分析结构质量
        avg_structural_quality = np.mean([ann.quality_metrics.structural_quality for ann in annotations])
        if avg_structural_quality < 0.7:
            recommendations.append("建议改善文档结构和格式规范")
        
        return recommendations


# 全局实例
intelligent_data_processor = IntelligentDataProcessor({})
