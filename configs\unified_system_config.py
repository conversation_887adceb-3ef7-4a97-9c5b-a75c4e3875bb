#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一系统配置管理器 - 白银市电力故障诊断系统
整合所有配置文件，建立统一的配置管理系统，提升系统可维护性

核心功能：
1. 统一配置文件管理
2. 环境变量支持
3. 配置验证和校验
4. 动态配置更新
5. 配置版本控制
6. 安全配置管理
"""

import os
import json
import yaml
import logging
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum
from pathlib import Path
import hashlib
from datetime import datetime

logger = logging.getLogger(__name__)


class ConfigLevel(Enum):
    """配置级别"""
    DEVELOPMENT = "development"
    TESTING = "testing"
    PRODUCTION = "production"


class ComponentType(Enum):
    """组件类型"""
    DATABASE = "database"
    API = "api"
    MODEL = "model"
    RETRIEVAL = "retrieval"
    PROCESSING = "processing"
    UI = "ui"
    MONITORING = "monitoring"


@dataclass
class ConfigValidationRule:
    """配置验证规则"""
    field_path: str
    required: bool = True
    data_type: type = str
    min_value: Optional[Union[int, float]] = None
    max_value: Optional[Union[int, float]] = None
    allowed_values: Optional[List[Any]] = None
    pattern: Optional[str] = None


class UnifiedSystemConfig:
    """统一系统配置管理器"""
    
    def __init__(self, config_dir: str = "configs", environment: str = "development"):
        self.config_dir = Path(config_dir)
        self.environment = environment
        self.config_data = {}
        self.validation_rules = []
        
        # 配置文件路径
        self.config_files = {
            "main": self.config_dir / "main_config.yaml",
            "database": self.config_dir / "database_config.yaml", 
            "api": self.config_dir / "api_config.yaml",
            "models": self.config_dir / "model_config.yaml",
            "retrieval": self.config_dir / "retrieval_config.yaml",
            "processing": self.config_dir / "processing_config.yaml",
            "ui": self.config_dir / "ui_config.yaml",
            "monitoring": self.config_dir / "monitoring_config.yaml"
        }
        
        # 初始化配置
        self._initialize_default_config()
        self._setup_validation_rules()
        self._load_all_configs()
    
    def _initialize_default_config(self):
        """初始化默认配置"""
        self.config_data = {
            # 系统基础配置
            "system": {
                "name": "白银市电力故障诊断系统",
                "version": "2.0.0",
                "environment": self.environment,
                "debug": self.environment == "development",
                "log_level": "INFO" if self.environment == "production" else "DEBUG",
                "timezone": "Asia/Shanghai",
                "language": "zh-CN"
            },
            
            # 数据库配置
            "database": {
                "chroma": {
                    "path": "./embeddings/chroma_store",
                    "collection_name": "power_fault_collection",
                    "persist_directory": "./embeddings/chroma_persist",
                    "embedding_function": "bge-large-zh-v1.5"
                },
                "faiss": {
                    "index_path": "./embeddings/faiss_index",
                    "dimension": 1024,
                    "index_type": "IVF"
                },
                "knowledge_base": {
                    "path": "knowledge_base",
                    "text_path": "knowledge_base/text",
                    "image_path": "knowledge_base/images",
                    "processed_path": "knowledge_base/processed"
                }
            },
            
            # API配置
            "api": {
                "deepseek": {
                    "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1",
                    "api_key": os.getenv("DEEPSEEK_API_KEY", "***********************************"),
                    "models": {
                        "deepseek-v3": {
                            "temperature": 0.7,
                            "max_tokens": 2000,
                            "stream": True
                        },
                        "deepseek-r1": {
                            "temperature": 0.3,
                            "max_tokens": 4000,
                            "stream": True,
                            "reasoning": True
                        }
                    },
                    "timeout": 60,
                    "max_retries": 3
                }
            },
            
            # 检索系统配置
            "retrieval": {
                "unified_retriever": {
                    "cache_enabled": True,
                    "cache_ttl": 3600,
                    "max_results": 10,
                    "strategies": {
                        "dense_weight": 0.4,
                        "sparse_weight": 0.3,
                        "professional_weight": 0.3
                    }
                },
                "advanced_rag": {
                    "hyde_enabled": True,
                    "self_rag_enabled": True,
                    "raptor_enabled": True,
                    "query_rewriting_enabled": True,
                    "technique_weights": {
                        "hyde": 0.25,
                        "self_rag": 0.25,
                        "raptor": 0.20,
                        "query_rewriting": 0.15,
                        "multi_hop": 0.10,
                        "context_compression": 0.05
                    }
                }
            },
            
            # 数据处理配置
            "processing": {
                "data_quality": {
                    "quality_threshold": 0.8,
                    "completeness_weight": 0.15,
                    "accuracy_weight": 0.20,
                    "consistency_weight": 0.15,
                    "timeliness_weight": 0.10,
                    "validity_weight": 0.15,
                    "uniqueness_weight": 0.10,
                    "professional_depth_weight": 0.10,
                    "structural_quality_weight": 0.05
                },
                "annotation": {
                    "auto_annotation": True,
                    "entity_extraction": True,
                    "relationship_extraction": True,
                    "term_standardization": True
                }
            },
            
            # 推理优化配置
            "reasoning": {
                "deepseek_r1": {
                    "max_reasoning_steps": 7,
                    "min_confidence_threshold": 0.6,
                    "self_correction_enabled": True,
                    "quality_threshold": 0.8,
                    "grpo_learning_rate": 0.01,
                    "exploration_rate": 0.1
                }
            },
            
            # 输出格式配置
            "output": {
                "report_formatter": {
                    "default_format": "structured_text",
                    "table_style": "professional",
                    "number_precision": 2,
                    "date_format": "%Y-%m-%d %H:%M:%S",
                    "include_metadata": True,
                    "compliance_check": True
                }
            },
            
            # UI配置
            "ui": {
                "server": {
                    "host": "0.0.0.0",
                    "port": 5002,
                    "debug": self.environment == "development"
                },
                "features": {
                    "knowledge_base_enabled": True,
                    "fault_analysis_enabled": True,
                    "equipment_management_enabled": True,
                    "report_generation_enabled": True,
                    "advanced_rag_enabled": True
                }
            },
            
            # 监控配置
            "monitoring": {
                "logging": {
                    "level": "INFO" if self.environment == "production" else "DEBUG",
                    "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s",
                    "file_path": "logs/system.log",
                    "max_file_size": "10MB",
                    "backup_count": 5
                },
                "metrics": {
                    "enabled": True,
                    "collection_interval": 60,
                    "retention_days": 30
                },
                "alerts": {
                    "enabled": True,
                    "error_threshold": 10,
                    "response_time_threshold": 5.0
                }
            },
            
            # 安全配置
            "security": {
                "api_rate_limit": {
                    "enabled": True,
                    "requests_per_minute": 100,
                    "burst_limit": 20
                },
                "data_encryption": {
                    "enabled": self.environment == "production",
                    "algorithm": "AES-256"
                },
                "access_control": {
                    "enabled": True,
                    "session_timeout": 3600
                }
            }
        }
    
    def _setup_validation_rules(self):
        """设置配置验证规则"""
        self.validation_rules = [
            # 系统配置验证
            ConfigValidationRule("system.name", required=True, data_type=str),
            ConfigValidationRule("system.version", required=True, data_type=str),
            ConfigValidationRule("system.environment", required=True, data_type=str, 
                               allowed_values=["development", "testing", "production"]),
            
            # API配置验证
            ConfigValidationRule("api.deepseek.api_key", required=True, data_type=str),
            ConfigValidationRule("api.deepseek.timeout", required=True, data_type=int, 
                               min_value=10, max_value=300),
            ConfigValidationRule("api.deepseek.max_retries", required=True, data_type=int,
                               min_value=1, max_value=10),
            
            # 检索配置验证
            ConfigValidationRule("retrieval.unified_retriever.cache_ttl", required=True, 
                               data_type=int, min_value=300, max_value=86400),
            ConfigValidationRule("retrieval.unified_retriever.max_results", required=True,
                               data_type=int, min_value=1, max_value=50),
            
            # 数据处理配置验证
            ConfigValidationRule("processing.data_quality.quality_threshold", required=True,
                               data_type=float, min_value=0.0, max_value=1.0),
            
            # UI配置验证
            ConfigValidationRule("ui.server.port", required=True, data_type=int,
                               min_value=1000, max_value=65535),
            
            # 监控配置验证
            ConfigValidationRule("monitoring.metrics.collection_interval", required=True,
                               data_type=int, min_value=10, max_value=3600),
            ConfigValidationRule("monitoring.metrics.retention_days", required=True,
                               data_type=int, min_value=1, max_value=365)
        ]
    
    def _load_all_configs(self):
        """加载所有配置文件"""
        try:
            # 创建配置目录
            self.config_dir.mkdir(exist_ok=True)
            
            # 加载或创建配置文件
            for config_name, config_path in self.config_files.items():
                if config_path.exists():
                    self._load_config_file(config_name, config_path)
                else:
                    self._create_default_config_file(config_name, config_path)
            
            # 应用环境变量覆盖
            self._apply_environment_overrides()
            
            # 验证配置
            self._validate_config()
            
            logger.info(f"配置加载完成，环境: {self.environment}")
            
        except Exception as e:
            logger.error(f"配置加载失败: {e}")
            raise
    
    def _load_config_file(self, config_name: str, config_path: Path):
        """加载单个配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                if config_path.suffix.lower() == '.yaml':
                    file_config = yaml.safe_load(f)
                else:
                    file_config = json.load(f)
            
            # 合并配置
            if file_config:
                self._deep_merge_config(self.config_data, file_config)
            
            logger.debug(f"加载配置文件: {config_path}")
            
        except Exception as e:
            logger.warning(f"加载配置文件失败 {config_path}: {e}")
    
    def _create_default_config_file(self, config_name: str, config_path: Path):
        """创建默认配置文件"""
        try:
            # 提取相关配置部分
            if config_name == "main":
                config_section = {"system": self.config_data["system"]}
            elif config_name in self.config_data:
                config_section = {config_name: self.config_data[config_name]}
            else:
                config_section = {}
            
            # 写入配置文件
            with open(config_path, 'w', encoding='utf-8') as f:
                if config_path.suffix.lower() == '.yaml':
                    yaml.dump(config_section, f, default_flow_style=False, 
                             allow_unicode=True, indent=2)
                else:
                    json.dump(config_section, f, ensure_ascii=False, indent=2)
            
            logger.info(f"创建默认配置文件: {config_path}")
            
        except Exception as e:
            logger.error(f"创建配置文件失败 {config_path}: {e}")
    
    def _deep_merge_config(self, base_config: Dict, new_config: Dict):
        """深度合并配置"""
        for key, value in new_config.items():
            if key in base_config and isinstance(base_config[key], dict) and isinstance(value, dict):
                self._deep_merge_config(base_config[key], value)
            else:
                base_config[key] = value
    
    def _apply_environment_overrides(self):
        """应用环境变量覆盖"""
        # 环境变量映射
        env_mappings = {
            "DEEPSEEK_API_KEY": "api.deepseek.api_key",
            "SYSTEM_DEBUG": "system.debug",
            "UI_PORT": "ui.server.port",
            "LOG_LEVEL": "system.log_level",
            "CACHE_TTL": "retrieval.unified_retriever.cache_ttl"
        }
        
        for env_var, config_path in env_mappings.items():
            env_value = os.getenv(env_var)
            if env_value:
                self._set_config_value(config_path, env_value)
                logger.debug(f"环境变量覆盖: {env_var} -> {config_path}")
    
    def _set_config_value(self, path: str, value: Any):
        """设置配置值"""
        keys = path.split('.')
        config = self.config_data
        
        for key in keys[:-1]:
            if key not in config:
                config[key] = {}
            config = config[key]
        
        # 类型转换
        final_key = keys[-1]
        if isinstance(config.get(final_key), bool):
            value = str(value).lower() in ('true', '1', 'yes', 'on')
        elif isinstance(config.get(final_key), int):
            value = int(value)
        elif isinstance(config.get(final_key), float):
            value = float(value)
        
        config[final_key] = value
    
    def _validate_config(self):
        """验证配置"""
        validation_errors = []
        
        for rule in self.validation_rules:
            try:
                value = self._get_config_value(rule.field_path)
                
                # 必需字段检查
                if rule.required and value is None:
                    validation_errors.append(f"必需字段缺失: {rule.field_path}")
                    continue
                
                if value is None:
                    continue
                
                # 数据类型检查
                if not isinstance(value, rule.data_type):
                    validation_errors.append(f"数据类型错误: {rule.field_path}, 期望 {rule.data_type.__name__}, 实际 {type(value).__name__}")
                
                # 数值范围检查
                if rule.min_value is not None and value < rule.min_value:
                    validation_errors.append(f"数值过小: {rule.field_path}, 最小值 {rule.min_value}")
                
                if rule.max_value is not None and value > rule.max_value:
                    validation_errors.append(f"数值过大: {rule.field_path}, 最大值 {rule.max_value}")
                
                # 允许值检查
                if rule.allowed_values and value not in rule.allowed_values:
                    validation_errors.append(f"值不在允许范围: {rule.field_path}, 允许值 {rule.allowed_values}")
                
            except Exception as e:
                validation_errors.append(f"验证字段失败: {rule.field_path}, 错误: {str(e)}")
        
        if validation_errors:
            error_msg = "配置验证失败:\n" + "\n".join(validation_errors)
            logger.error(error_msg)
            raise ValueError(error_msg)
        
        logger.info("配置验证通过")
    
    def _get_config_value(self, path: str) -> Any:
        """获取配置值"""
        keys = path.split('.')
        value = self.config_data
        
        for key in keys:
            if isinstance(value, dict) and key in value:
                value = value[key]
            else:
                return None
        
        return value
    
    def get(self, path: str, default: Any = None) -> Any:
        """获取配置值"""
        value = self._get_config_value(path)
        return value if value is not None else default
    
    def set(self, path: str, value: Any):
        """设置配置值"""
        self._set_config_value(path, value)
    
    def get_component_config(self, component: ComponentType) -> Dict[str, Any]:
        """获取组件配置"""
        component_configs = {
            ComponentType.DATABASE: self.config_data.get("database", {}),
            ComponentType.API: self.config_data.get("api", {}),
            ComponentType.RETRIEVAL: self.config_data.get("retrieval", {}),
            ComponentType.PROCESSING: self.config_data.get("processing", {}),
            ComponentType.UI: self.config_data.get("ui", {}),
            ComponentType.MONITORING: self.config_data.get("monitoring", {})
        }
        
        return component_configs.get(component, {})
    
    def save_config(self, config_name: str = "main"):
        """保存配置到文件"""
        try:
            config_path = self.config_files.get(config_name)
            if not config_path:
                raise ValueError(f"未知配置文件: {config_name}")
            
            # 提取相关配置部分
            if config_name == "main":
                config_section = {"system": self.config_data["system"]}
            elif config_name in self.config_data:
                config_section = {config_name: self.config_data[config_name]}
            else:
                config_section = self.config_data
            
            # 写入文件
            with open(config_path, 'w', encoding='utf-8') as f:
                if config_path.suffix.lower() == '.yaml':
                    yaml.dump(config_section, f, default_flow_style=False,
                             allow_unicode=True, indent=2)
                else:
                    json.dump(config_section, f, ensure_ascii=False, indent=2)
            
            logger.info(f"配置已保存: {config_path}")
            
        except Exception as e:
            logger.error(f"保存配置失败: {e}")
            raise
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            "system_info": {
                "name": self.config_data["system"]["name"],
                "version": self.config_data["system"]["version"],
                "environment": self.config_data["system"]["environment"]
            },
            "components_enabled": {
                "deepseek_api": bool(self.config_data["api"]["deepseek"]["api_key"]),
                "unified_retriever": self.config_data["retrieval"]["unified_retriever"]["cache_enabled"],
                "advanced_rag": self.config_data["retrieval"]["advanced_rag"]["hyde_enabled"],
                "data_processing": self.config_data["processing"]["annotation"]["auto_annotation"],
                "monitoring": self.config_data["monitoring"]["metrics"]["enabled"]
            },
            "config_files": {name: path.exists() for name, path in self.config_files.items()},
            "validation_status": "通过",
            "last_updated": datetime.now().isoformat()
        }
    
    def reload_config(self):
        """重新加载配置"""
        logger.info("重新加载配置...")
        self._initialize_default_config()
        self._load_all_configs()
        logger.info("配置重新加载完成")


# 全局配置实例
unified_config = UnifiedSystemConfig(
    environment=os.getenv("SYSTEM_ENVIRONMENT", "development")
)

# 便捷访问函数
def get_config(path: str, default: Any = None) -> Any:
    """获取配置值"""
    return unified_config.get(path, default)

def get_component_config(component: ComponentType) -> Dict[str, Any]:
    """获取组件配置"""
    return unified_config.get_component_config(component)
